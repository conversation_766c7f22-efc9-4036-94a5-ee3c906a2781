{"name": "sgs-gita-alumni", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run detect-mock-data && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:unit": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:api": "playwright test tests/api/", "test:auth": "playwright test tests/e2e/auth.spec.ts", "test:dashboard": "playwright test tests/e2e/dashboard.spec.ts", "test:responsive": "playwright test tests/e2e/responsive.spec.ts", "test:cross-browser": "playwright test tests/e2e/cross-browser.spec.ts", "test:performance": "playwright test tests/e2e/performance.spec.ts", "test:all": "npm run test:run && npm run test:e2e", "test:comprehensive": "node scripts/test-runner.js all --report", "test:quick": "node scripts/test-runner.js unit e2e", "test:ci": "playwright test --reporter=html", "test:report": "playwright show-report", "test:allure": "allure generate test-results/allure-results --clean && allure open", "test:json": "playwright test --reporter=json", "test:junit": "playwright test --reporter=junit", "test:clear": "rm -rf test-results/ && rm -rf coverage/", "test:db:check": "node scripts/check-database.js", "test:db:reset": "node scripts/reset-test-database.js", "test:db:seed": "node scripts/seed-test-data.js", "test:security": "playwright test tests/e2e/security.spec.ts", "test:accessibility": "playwright test tests/e2e/accessibility.spec.ts", "test:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari'", "test:tablet": "playwright test --project='iPad' --project='iPad Pro'", "test:desktop": "playwright test --project=chromium --project=firefox --project=webkit", "test:load": "playwright test tests/e2e/load.spec.ts", "test:stress": "playwright test tests/e2e/stress.spec.ts", "test:smoke": "playwright test tests/e2e/smoke.spec.ts", "test:regression": "npm run test:all && npm run test:performance", "test:coverage": "vitest run --coverage && playwright test --reporter=html", "test:watch": "vitest --watch", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:chrome": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:safari": "playwright test --project=webkit", "test:edge": "playwright test --project='Microsoft Edge'", "test:install": "playwright install", "test:install-deps": "playwright install-deps", "test:codegen": "playwright codegen", "test:trace": "playwright show-trace", "test:ui-mode": "playwright test --ui", "test:update-snapshots": "playwright test --update-snapshots", "test:grep": "playwright test --grep", "test:grep-invert": "playwright test --grep-invert", "test:max-failures": "playwright test --max-failures", "test:retries": "playwright test --retries", "test:timeout": "playwright test --timeout", "test:workers": "playwright test --workers", "test:reporter": "playwright test --reporter", "test:output": "playwright test --output", "test:config": "playwright test --config", "test:project": "playwright test --project", "test:global-timeout": "playwright test --global-timeout", "test:forbid-only": "playwright test --forbid-only", "test:fully-parallel": "playwright test --fully-parallel", "test:shard": "playwright test --shard", "test:list": "playwright test --list", "test:help": "playwright test --help", "prepare": "husky", "check-redundancy": "jscpd", "analyze-bundle": "ANALYZE=true npm run build", "quality-check": "npm run lint && npm run check-redundancy && npm run detect-mock-data", "validate-docs": "node scripts/validate-documentation-standards.js", "validate-docs-standards": "node scripts/validate-documentation-standards.js", "validate-deployment": "node scripts/deployment-validation.js", "detect-mock-data": "node scripts/detect-mock-data.js", "scan-mock-data": "node scripts/detect-mock-data.js", "audit-mock-data": "node scripts/detect-mock-data.js", "validate-no-mock-usage": "node scripts/detect-mock-data.js"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.883.0", "@aws-sdk/lib-dynamodb": "^3.883.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.13", "@sentry/browser": "^10.10.0", "@sentry/react": "^10.10.0", "@sentry/replay": "^7.116.0", "@sentry/tracing": "^7.120.4", "@tanstack/react-table": "^8.20.5", "@types/dompurify": "^3.0.5", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.7.7", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^3.6.0", "dompurify": "^3.2.7", "dotenv": "^17.2.2", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.441.0", "mysql2": "^3.14.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "sgs-gita-alumni": "file:", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^13.0.0", "web-vitals": "^5.1.0"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.3.1", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "@vitejs/plugin-react": "^4.3.3", "allure-playwright": "^2.15.1", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.12", "eslint-plugin-sonarjs": "^3.0.5", "globals": "^16.3.0", "husky": "^9.1.7", "jscpd": "^4.0.5", "jsdom": "^26.1.0", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "typescript": "^5.6.3", "vite": "^5.4.9", "vitest": "^3.2.4", "webpack-bundle-analyzer": "^4.10.2"}}