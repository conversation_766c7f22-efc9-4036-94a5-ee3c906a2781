// Check alumni_members table for data related to user 4600
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const DB_CONFIG = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3306'),
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000,
};

async function checkAlumniMembers() {
  let connection;

  try {
    connection = await mysql.createConnection(DB_CONFIG);

    console.log('Checking alumni_members table...');

    // Check if there's any data in alumni_members
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM alumni_members');
    console.log(`Total records in alumni_members: ${countResult[0].total}`);

    // Look for records that might match user 4600 by user_id
    const [userIdMatches] = await connection.execute(
      'SELECT id, family_name, father_name, student_id, user_id FROM alumni_members WHERE user_id = ?',
      [4600]
    );

    if (userIdMatches.length > 0) {
      console.log('Found alumni_members records with user_id = 4600:');
      console.log(userIdMatches);
    } else {
      console.log('No alumni_members records found with user_id = 4600');
    }

    // Check all records in alumni_members (limit to first 10)
    const [allRecords] = await connection.execute(
      'SELECT id, family_name, father_name, student_id, user_id FROM alumni_members LIMIT 10'
    );

    console.log('First 10 records in alumni_members:');
    console.log(allRecords);

    // Check if user 4600 has any alumni_member_id set
    const [userResult] = await connection.execute(
      'SELECT id, email, alumni_member_id FROM app_users WHERE id = ?',
      [4600]
    );

    // Check for alumni_members records without user_id (orphaned records)
    const [orphanedRecords] = await connection.execute(
      'SELECT id, family_name, father_name, student_id FROM alumni_members WHERE user_id IS NULL LIMIT 5'
    );

    console.log('Orphaned alumni_members records (no user_id):');
    console.log(orphanedRecords);

    console.log('User 4600 data:');
    console.log(userResult[0]);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkAlumniMembers();